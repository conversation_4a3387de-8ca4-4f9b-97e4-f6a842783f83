{"symbol": "XRPUSDC", "primaryTimeframe": "1s", "featureParquetDir": "parquet_processed", "use_1s_decisions": true, "dataProvider": {"symbol": "XRPUSDC"}, "coinapi": {"apiKey": "${COINAPI_KEY}"}, "binance": {"apiKey": "${BINANCE_API_KEY}", "apiSecret": "${BINANCE_API_SECRET}", "futures": true, "leverage": 3, "marginType": "ISOLATED"}, "indicatorSettings": {"stateLookback": 30, "orderFlowFeatures": {"enabled": true, "sourceTf": "trades", "volumeDeltaPeriods": ["30s", "2m"], "tradeCountDeltaPeriods": ["100t", "500t"], "cvdEnabled": true, "cvdResetFrequency": "daily"}, "hmm_5m": {"enabled": true, "tf": "5m", "n_components": 3, "window": 200, "data_source": "volatility"}, "atr_5m": {"enabled": true, "tf": "5m", "basePeriod": 14}, "vwap_5m": {"enabled": true, "tf": "5m"}, "volume": {"enabled": true}, "volumeImbalance_5m": {"enabled": true, "tf": "5m"}, "bollinger_5m": {"enabled": true, "tf": "5m", "basePeriod": 20, "baseStDev": 2.0}, "adx": {"enabled": true, "tf": "5m", "basePeriod": 14}, "rsi_5m": {"enabled": true, "tf": "5m", "basePeriod": 14}, "ema_5m": {"enabled": true, "tf": "5m", "baseFastPeriod": 9, "baseSlowPeriod": 21}}, "riskManagement": {"positionSizingMethod": "RiskPercentage", "riskPerTradePercentage": 3, "maxPositionSizePercentEquity": 100.0, "maxDailyRiskPercent": 3}, "debug": false, "tradeParams": {"slippagePercentage": 0.01, "feePercentage": 0.05, "entryActionThreshold": 0.8, "exitActionThreshold": 0.8, "longEntryThreshold": 0.6, "shortEntryThreshold": -0.2, "minAtrPercentOfPrice": 0, "minSLDistanceATR": 3.0, "minSLDistancePercent": 20, "stopLossATR": 10.0, "takeProfitATR": 35.0, "rrTarget": 3.5, "tpMode": "split", "splitTpEnabled": true, "splitTpFirstTarget": 2.0, "splitTpFirstPercent": 0.5, "splitTpSecondTarget": 4.0, "stopLossCapitalPercent": 1.5, "tpCapitalPercentage": 0.8, "agentExitsEnabled": false, "minTimeInTradeSeconds": 300, "enableDynamicThresholds": false, "volatilityAdjustmentFactor": 0.5, "maxVolatilityThreshold": 0.004, "maskOrderBook": false, "orderBookMaskingRatio": 0.0, "useSmoothedSignals": true, "signalSmoothingFactor": 0.8, "biasCorrection": {"enabled": false, "centeringOffset": 0.3, "scalingFactor": 1.5}, "trendFilter": {"enabled": false, "method": "supertrend_adx", "timeframe": "1h", "params": {"supertrend": {"atrPeriod": 10, "multiplier": 3.0}, "adx": {"period": 14, "minAdx": 25}}}, "signalSmoothing": {"method": "kama", "emaAlpha": 0.7, "kamaFastSC": 0.666, "kamaSlowSC": 0.064, "medianWindow": 5, "kalmanProcessNoise": 0.01, "kalmanMeasurementNoise": 0.1, "multiPredictionCount": 7, "trendConfirmationEnabled": false, "trendEmaShort": 3, "trendEmaLong": 8}, "thresholdTuning": {"confidenceWeight": 0.02, "volatilityWeight": 0.02, "adaptiveThresholdsEnabled": false, "maxAdjustment": 0.03, "minConfidence": 0.3}, "_comment": "OPTIMIZED: Higher thresholds (0.9) with relaxed stability, wider SL/TP (0.20% min), split TP strategy (50% at 2R, 50% trail), bias correction enabled, reduced fees (0.03%), TSL only after first TP."}, "blackoutHours": [], "blackoutDates": ["monthday==1", "weekday==5", "weekday==6"], "account": {"initialEquity": 100}, "testMode": false, "trailingStopLoss": {"enabled": true, "activatePercentProfit": 1.0, "trailPercentDistance": 0.8, "onlyAfterFirstTp": true}, "trainingSettings": {"totalTimesteps": ********, "logDir": "./tensorboard_logs/", "modelSavePath": "./sac_9996800_steps", "n_envs": 256, "_learningRateNote": "SGDR scheduler with warm restarts is hardcoded in agent.py", "bufferSize": 5000000, "learningStarts": 200000, "batchSize": 1024, "evalFreq": 400000, "evalEpisodes": 5, "seed": 42, "trainSplitRatio": 0.9, "netArch": [512, 256], "featureExtractor": "cnn", "featureExtractorKwargs": {"lookback": 30, "n_filters": 128, "kernel": 3, "out_dim": 256}, "sacParams": {"tau": 0.005, "gamma": 0.99, "gradient_steps": 64, "train_freq": [16, "step"], "ent_coef": "auto_0.3", "target_entropy": -2.4, "target_update_interval": 2, "max_grad_norm": 1.0}, "rewardStructure": {"pnlScale": 0.8, "tpBonusBase": 0.0, "slPenalty": -2.5, "bigLossPenalty": -18.0, "holdPenaltyPerStep": -0.0008, "profitableHoldBonus": 0.25, "inactivityPenalty": -0.0005, "underwaterPenalty": -0.0005, "exitBonusMultiplier": 0.2, "rewardClipMin": -10.0, "rewardClipMax": 25.0}}, "backtestSettings": {"startTime": "2025-01-04T00:00:00Z", "endTime": "2025-04-24T23:59:59Z"}, "envSettings": {"state_lookback": 30, "max_ep_len": 86400, "inactivity_limit": 3600, "feature_columns": ["open", "high", "low", "close", "volume", "buy_volume", "sell_volume", "trade_count", "vwap", "ATR_14", "RSI_14", "EMA_9", "EMA_21", "ADX_14", "DMP_14", "DMN_14", "volume_delta_30s", "volume_delta_2m", "trade_count_delta_100t", "trade_count_delta_500t", "cvd_reset_daily", "hmm_state_3c_volatility_5m", "VWAP_pta", "bollinger_bands_upper_20_2.0", "bollinger_bands_middle_20_2.0", "bollinger_bands_lower_20_2.0", "bollinger_bands_width_20_2.0", "spread", "mid_price", "tob_imbalance", "depth_imbalance5", "depth_slope5", "ob_bid_vol_l1", "ob_ask_vol_l1", "ob_bid_vol_l2", "ob_ask_vol_l2", "ob_bid_vol_l3", "ob_ask_vol_l3", "ob_bid_vol_l4", "ob_ask_vol_l4", "ob_bid_vol_l5", "ob_ask_vol_l5", "dvol_bid_l1", "dvol_ask_l1", "trade_dir_sum_1s", "trade_skew_1s", "dt_since_buy", "dt_since_sell"]}}